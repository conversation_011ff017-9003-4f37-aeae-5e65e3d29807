--[[
    Advanced Space Combat - CAP Validation Fix Test v1.0.0
    
    Quick test specifically for the CAP validation GenerateSummary error fix.
    This addresses the "attempt to index local 'categoryResults' (a number value)" error.
]]

-- Initialize CAP Validation Fix Test namespace
ASC = ASC or {}
ASC.CAPValidationFixTest = ASC.CAPValidationFixTest or {}

-- Test the specific CAP validation fix
function ASC.CAPValidationFixTest.TestGenerateSummaryFix()
    print("[CAP Fix Test] Testing CAP validation GenerateSummary fix...")
    
    local testResults = {
        passed = 0,
        failed = 0,
        errors = {},
        warnings = {}
    }
    
    -- Test 1: Check if CAP validation system exists
    if not ASC.CAP or not ASC.CAP.Validation then
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "CAP validation system not found")
        return false, testResults
    end
    
    testResults.passed = testResults.passed + 1
    print("[CAP Fix Test] ✓ CAP validation system found")
    
    -- Test 2: Create a mock results structure like the one that was causing errors
    local mockResults = {
        detection = {passed = 1, failed = 0, errors = {}, warnings = {}},
        communication = {passed = 0, failed = 1, errors = {"Test error"}, warnings = {}},
        resource = {passed = 2, failed = 0, errors = {}, warnings = {"Test warning"}},
        fallback = {passed = 1, failed = 1, errors = {"Test fallback error"}, warnings = {}},
        performance = {
            validationTime = 0.123,  -- This was causing the error (number value)
            timestamp = "2024-01-01 12:00:00"  -- This was also causing issues (string value)
        }
    }
    
    -- Test 3: Try to run GenerateSummary with the mock data
    if ASC.CAP.Validation.GenerateSummary then
        local success, error = pcall(function()
            ASC.CAP.Validation.GenerateSummary(mockResults)
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[CAP Fix Test] ✓ GenerateSummary handles mixed data types correctly")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "GenerateSummary still fails with mixed types: " .. tostring(error))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "GenerateSummary function not found")
    end
    
    -- Test 4: Try to run the full validation process
    if ASC.CAP.Validation.RunValidation then
        local success, error = pcall(function()
            return ASC.CAP.Validation.RunValidation()
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[CAP Fix Test] ✓ Full CAP validation runs without errors")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "Full CAP validation still fails: " .. tostring(error))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "RunValidation function not found")
    end
    
    -- Test 5: Test edge cases with different data structures
    local edgeCaseResults = {
        stringValue = "test",  -- String value
        numberValue = 42,      -- Number value
        booleanValue = true,   -- Boolean value
        nilValue = nil,        -- Nil value
        validTest = {passed = 1, failed = 0, errors = {}, warnings = {}},  -- Valid test result
        invalidTest = {someOtherProperty = "value"}  -- Invalid test result structure
    }
    
    if ASC.CAP.Validation.GenerateSummary then
        local success, error = pcall(function()
            ASC.CAP.Validation.GenerateSummary(edgeCaseResults)
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[CAP Fix Test] ✓ GenerateSummary handles edge cases correctly")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "GenerateSummary fails with edge cases: " .. tostring(error))
        end
    end
    
    local allPassed = testResults.failed == 0
    print("[CAP Fix Test] CAP Validation Fix: " .. (allPassed and "✓ WORKING" or "✗ FAILED"))
    
    return allPassed, testResults
end

-- Run comprehensive CAP validation fix test
function ASC.CAPValidationFixTest.RunAllTests()
    print("[CAP Fix Test] ==========================================")
    print("[CAP Fix Test] CAP Validation Fix Test")
    print("[CAP Fix Test] ==========================================")
    
    local startTime = CurTime()
    
    -- Run the main test
    local success, results = ASC.CAPValidationFixTest.TestGenerateSummaryFix()
    
    local testTime = CurTime() - startTime
    
    -- Print summary
    print("[CAP Fix Test] ==========================================")
    print("[CAP Fix Test] TEST SUMMARY")
    print("[CAP Fix Test] ==========================================")
    print("[CAP Fix Test] Overall Status: " .. (success and "✅ ALL TESTS PASSED" or "❌ SOME TESTS FAILED"))
    print("[CAP Fix Test] Tests Passed: " .. results.passed)
    print("[CAP Fix Test] Tests Failed: " .. results.failed)
    print("[CAP Fix Test] Test Duration: " .. string.format("%.3fs", testTime))
    
    if #results.errors > 0 then
        print("[CAP Fix Test] ERRORS:")
        for _, error in ipairs(results.errors) do
            print("[CAP Fix Test]   - " .. error)
        end
    end
    
    if #results.warnings > 0 then
        print("[CAP Fix Test] WARNINGS:")
        for _, warning in ipairs(results.warnings) do
            print("[CAP Fix Test]   - " .. warning)
        end
    end
    
    if success then
        print("[CAP Fix Test] 🎉 CAP validation GenerateSummary error has been FIXED!")
        print("[CAP Fix Test] The system now properly handles mixed data types.")
    else
        print("[CAP Fix Test] ❌ CAP validation fix needs more work.")
    end
    
    print("[CAP Fix Test] ==========================================")
    
    return success
end

-- Console command for testing the CAP validation fix
concommand.Add("asc_test_cap_validation_fix", function(ply, cmd, args)
    local success = ASC.CAPValidationFixTest.RunAllTests()
    
    local msg = "[CAP Fix Test] " .. (success and "✅ CAP validation fix is working!" or "❌ CAP validation fix has issues")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Quick status command
concommand.Add("asc_cap_fix_status", function(ply, cmd, args)
    local msg = "[CAP Fix Test] Testing CAP validation fix..."
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
    
    -- Run quick test
    local success, results = ASC.CAPValidationFixTest.TestGenerateSummaryFix()
    
    local statusMsg = "[CAP Fix Test] Status: " .. (success and "✅ WORKING" or "❌ FAILED") .. 
                     " (" .. results.passed .. " passed, " .. results.failed .. " failed)"
    
    if IsValid(ply) then
        ply:ChatPrint(statusMsg)
    else
        print(statusMsg)
    end
end)

-- Auto-test on load
timer.Simple(4, function()
    print("[CAP Fix Test] Running automatic CAP validation fix test...")
    local success = ASC.CAPValidationFixTest.RunAllTests()
    
    if success then
        print("[CAP Fix Test] ✅ Auto-test PASSED - CAP validation fix is working!")
    else
        print("[CAP Fix Test] ❌ Auto-test FAILED - CAP validation fix needs attention!")
    end
end)

print("[Advanced Space Combat] CAP Validation Fix Test v1.0.0 Loaded")
