--[[
    Advanced Space Combat - Czech Language Fix Validation v1.0.0
    
    Quick validation and testing system to ensure Czech language improvements
    are working correctly after the IPAddress() fix.
]]

-- Initialize validation namespace
ASC = ASC or {}
ASC.CzechValidation = ASC.CzechValidation or {}

-- Validation functions
ASC.CzechValidation = {
    -- Test if all Czech systems are loaded and working
    ValidateAllSystems = function()
        print("[Czech Validation] Testing Czech language system integrity...")
        
        local results = {
            systems = {},
            errors = {},
            warnings = {},
            totalTests = 0,
            passedTests = 0
        }
        
        -- Test 1: Czech Localization System
        results.totalTests = results.totalTests + 1
        if ASC.Czech and ASC.Czech.Config then
            results.systems.czech_localization = "✓ LOADED"
            results.passedTests = results.passedTests + 1
        else
            results.systems.czech_localization = "✗ MISSING"
            table.insert(results.errors, "Czech localization system not loaded")
        end
        
        -- Test 2: Czech Auto-Detection System
        results.totalTests = results.totalTests + 1
        if ASC.CzechDetection and ASC.CzechDetection.Core then
            results.systems.czech_detection = "✓ LOADED"
            results.passedTests = results.passedTests + 1
        else
            results.systems.czech_detection = "✗ MISSING"
            table.insert(results.errors, "Czech auto-detection system not loaded")
        end
        
        -- Test 3: AI System Czech Integration
        results.totalTests = results.totalTests + 1
        if ASC.AI and ASC.AI.Languages and ASC.AI.Languages.Database and ASC.AI.Languages.Database.czech then
            results.systems.ai_czech = "✓ INTEGRATED"
            results.passedTests = results.passedTests + 1
        else
            results.systems.ai_czech = "✗ NOT INTEGRATED"
            table.insert(results.errors, "AI system Czech integration missing")
        end
        
        -- Test 4: Multilingual System
        results.totalTests = results.totalTests + 1
        if ASC.Multilingual and ASC.Multilingual.Core then
            results.systems.multilingual = "✓ LOADED"
            results.passedTests = results.passedTests + 1
        else
            results.systems.multilingual = "✗ MISSING"
            table.insert(results.warnings, "Multilingual system not loaded (optional)")
        end
        
        -- Test 5: Czech Testing Framework
        results.totalTests = results.totalTests + 1
        if ASC.CzechTesting and ASC.CzechTesting.RunAllTests then
            results.systems.czech_testing = "✓ LOADED"
            results.passedTests = results.passedTests + 1
        else
            results.systems.czech_testing = "✗ MISSING"
            table.insert(results.warnings, "Czech testing framework not loaded (optional)")
        end
        
        -- Test 6: Czech Commands
        results.totalTests = results.totalTests + 1
        if ASC.CzechCommands then
            results.systems.czech_commands = "✓ LOADED"
            results.passedTests = results.passedTests + 1
        else
            results.systems.czech_commands = "✗ MISSING"
            table.insert(results.warnings, "Czech commands system not loaded (optional)")
        end
        
        return results
    end,
    
    -- Test Czech language detection specifically
    TestCzechDetection = function()
        print("[Czech Validation] Testing Czech language detection...")
        
        if not ASC.AI or not ASC.AI.Languages or not ASC.AI.Languages.DetectLanguage then
            print("✗ AI language detection not available")
            return false
        end
        
        local testCases = {
            {text = "ahoj jak se máš", expected = "czech"},
            {text = "pomoc s jádrem lodi", expected = "czech"},
            {text = "potřebuji nápovědu", expected = "czech"},
            {text = "hello how are you", expected = "english"},
            {text = "help with ship core", expected = "english"}
        }
        
        local passed = 0
        local total = #testCases
        
        for _, test in ipairs(testCases) do
            local detected = ASC.AI.Languages.DetectLanguage(test.text)
            if detected == test.expected then
                print("✓ '" .. test.text .. "' -> " .. detected)
                passed = passed + 1
            else
                print("✗ '" .. test.text .. "' -> " .. detected .. " (expected: " .. test.expected .. ")")
            end
        end
        
        print("[Czech Validation] Detection test: " .. passed .. "/" .. total .. " passed")
        return passed == total
    end,
    
    -- Test Czech encoding validation
    TestCzechEncoding = function()
        print("[Czech Validation] Testing Czech character encoding...")
        
        if not ASC.Czech or not ASC.Czech.ValidateUTF8 then
            print("✗ Czech UTF-8 validation not available")
            return false
        end
        
        local testText = "Testování českých znaků: áčďéěíňóřšťúůýž ÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ"
        local isValid = ASC.Czech.ValidateUTF8(testText)
        
        if isValid then
            print("✓ Czech encoding validation: PASSED")
            print("  Test text: " .. testText)
            return true
        else
            print("✗ Czech encoding validation: FAILED")
            print("  Test text: " .. testText)
            return false
        end
    end,
    
    -- Test if the IPAddress error is fixed
    TestAutoDetectionError = function()
        print("[Czech Validation] Testing auto-detection system for errors...")
        
        if not ASC.CzechDetection or not ASC.CzechDetection.Core then
            print("✗ Czech auto-detection system not available")
            return false
        end
        
        -- Try to run detection methods safely
        local success = true
        local errors = {}
        
        -- Test DetectSystemLocale (this was causing the IPAddress error)
        local testSuccess, testError = pcall(function()
            if ASC.CzechDetection.Core.DetectSystemLocale then
                -- Create a dummy player object for testing
                local dummyPlayer = {
                    Name = function() return "TestPlayer" end,
                    SteamID = function() return "STEAM_TEST" end,
                    IsValid = function() return true end
                }
                
                ASC.CzechDetection.Core.DetectSystemLocale(dummyPlayer)
            end
        end)
        
        if not testSuccess then
            success = false
            table.insert(errors, "DetectSystemLocale error: " .. tostring(testError))
        end
        
        if success then
            print("✓ Auto-detection system: NO ERRORS")
            return true
        else
            print("✗ Auto-detection system: ERRORS FOUND")
            for _, error in ipairs(errors) do
                print("  - " .. error)
            end
            return false
        end
    end
}

-- Console command for quick validation
concommand.Add("asc_validate_czech_fix", function(ply, cmd, args)
    local function sendMsg(message)
        if IsValid(ply) then
            ply:ChatPrint("[Czech Validation] " .. message)
        else
            print("[Czech Validation] " .. message)
        end
    end
    
    sendMsg("=== Czech Language System Validation ===")
    
    -- Run system validation
    local results = ASC.CzechValidation.ValidateAllSystems()
    
    sendMsg("System Status:")
    for system, status in pairs(results.systems) do
        sendMsg("  " .. system .. ": " .. status)
    end
    
    sendMsg("Overall: " .. results.passedTests .. "/" .. results.totalTests .. " systems OK")
    
    -- Show errors
    if #results.errors > 0 then
        sendMsg("Errors:")
        for _, error in ipairs(results.errors) do
            sendMsg("  ✗ " .. error)
        end
    end
    
    -- Show warnings
    if #results.warnings > 0 then
        sendMsg("Warnings:")
        for _, warning in ipairs(results.warnings) do
            sendMsg("  ⚠ " .. warning)
        end
    end
    
    -- Test specific functions
    sendMsg("=== Function Tests ===")
    
    local detectionTest = ASC.CzechValidation.TestCzechDetection()
    sendMsg("Czech Detection: " .. (detectionTest and "PASSED" or "FAILED"))
    
    local encodingTest = ASC.CzechValidation.TestCzechEncoding()
    sendMsg("Czech Encoding: " .. (encodingTest and "PASSED" or "FAILED"))
    
    local errorTest = ASC.CzechValidation.TestAutoDetectionError()
    sendMsg("Auto-Detection Fix: " .. (errorTest and "PASSED" or "FAILED"))
    
    -- Final result
    local allPassed = (results.passedTests >= 3) and detectionTest and encodingTest and errorTest
    sendMsg("=== FINAL RESULT ===")
    sendMsg("Czech Language System: " .. (allPassed and "✓ WORKING" or "✗ ISSUES FOUND"))
    
    if allPassed then
        sendMsg("🇨🇿 Czech language integration is working correctly!")
        sendMsg("You can now use: aria_czech help")
    else
        sendMsg("❌ Some issues were found. Check the logs above.")
    end
end)

-- Auto-run validation on load (delayed to ensure all systems are loaded)
timer.Simple(3, function()
    print("[Czech Validation] Running automatic validation...")
    
    local results = ASC.CzechValidation.ValidateAllSystems()
    local errorTest = ASC.CzechValidation.TestAutoDetectionError()
    
    if results.passedTests >= 3 and errorTest then
        print("[Czech Validation] ✓ Czech language system is working correctly!")
        print("[Czech Validation] IPAddress() error has been fixed.")
        print("[Czech Validation] Use 'asc_validate_czech_fix' for detailed testing.")
    else
        print("[Czech Validation] ⚠ Some Czech language systems may have issues.")
        print("[Czech Validation] Use 'asc_validate_czech_fix' for detailed diagnosis.")
    end
end)

print("[Advanced Space Combat] Czech Language Fix Validation v1.0.0 Loaded")
